import React, { useState } from "react";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, Trash2, Users, Clock, FileText, Wrench, Package, Cloud, Settings, Target, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Define the validation schema for the comprehensive investigation form
const comprehensiveInvestigationSchema = z.object({
  // Investigation Team
  investigationTeam: z.object({
    leadInvestigator: z.string().min(1, "Lead investigator is required"),
    teamMembers: z.array(z.object({
      name: z.string(),
      designation: z.string(),
    })).default([]),
    investigationStartDate: z.date().optional(),
    investigationEndDate: z.date().optional(),
    purposeOfInvestigation: z.string().optional(),
    consequenceOfInterest: z.string().optional(),
    timeframeOfInterest: z.string().optional(),
    peopleOfInterest: z.string().optional(),
    equipmentOfInterest: z.string().optional(),
    activitiesOfInterest: z.string().optional(),
    geographicalBoundariesOfInterest: z.string().optional(),
  }).optional(),

  // Sequence of Events
  sequenceOfEvents: z.object({
    description: z.string().optional(),
    incidentDate: z.date().optional(),
    incidentTime: z.string().optional(),
    incidentDescription: z.string().optional(),
    eventDescription: z.string().min(1, "Event description is required"),
    timeline: z.array(z.object({
      time: z.string(),
      event: z.string(),
      evidence: z.string().optional(),
    })).default([]),
    contributingFactors: z.string().optional(),
  }).optional(),

  // Information Gathering - People
  informationGatheringPeople: z.object({
    witnessInterviews: z.array(z.object({
      name: z.string(),
      role: z.string(),
      statement: z.string(),
      dateInterviewed: z.date(),
    })).default([]),
    injuredPersonStatement: z.string().optional(),
    supervisorStatement: z.string().optional(),
    otherPersonnelInvolved: z.string().optional(),
  }).optional(),

  // Information Gathering - Equipment
  informationGatheringEquipment: z.object({
    equipmentInvolved: z.string().optional(),
    equipmentCondition: z.string().optional(),
    maintenanceRecords: z.string().optional(),
    inspectionRecords: z.string().optional(),
    equipmentDefects: z.string().optional(),
    equipmentPhotos: z.array(z.string()).default([]),
  }).optional(),

  // Information Gathering - Material
  informationGatheringMaterial: z.object({
    materialsInvolved: z.string().optional(),
    materialCondition: z.string().optional(),
    materialSpecifications: z.string().optional(),
    materialSafetyDataSheets: z.string().optional(),
    materialHandlingProcedures: z.string().optional(),
    materialDefects: z.string().optional(),
  }).optional(),

  // Information Gathering - Environment
  informationGatheringEnvironment: z.object({
    weatherConditions: z.string().optional(),
    lightingConditions: z.string().optional(),
    noiseLevel: z.string().optional(),
    temperatureConditions: z.string().optional(),
    workspaceLayout: z.string().optional(),
    housekeepingConditions: z.string().optional(),
    environmentalHazards: z.string().optional(),
  }).optional(),

  // Information Gathering - Method
  informationGatheringMethod: z.object({
    workProcedures: z.string().optional(),
    trainingRecords: z.string().optional(),
    safetyProcedures: z.string().optional(),
    riskAssessments: z.string().optional(),
    permitToWork: z.string().optional(),
    supervisionLevel: z.string().optional(),
    communicationMethods: z.string().optional(),
  }).optional(),

  // Conclusions
  investigationConclusions: z.object({
    rootCauses: z.array(z.object({
      category: z.string(),
      description: z.string(),
      evidence: z.string(),
    })).default([]),
    contributingFactors: z.array(z.object({
      factor: z.string(),
      impact: z.string(),
    })).default([]),
    lessonsLearned: z.string().optional(),
    recommendations: z.array(z.object({
      recommendation: z.string(),
      priority: z.enum(['High', 'Medium', 'Low']),
      targetDate: z.date(),
      responsiblePerson: z.string(),
    })).default([]),
  }).optional(),

  // Action Controls
  actionControls: z.array(z.object({
    actionType: z.enum(['Immediate', 'Short-term', 'Long-term']),
    description: z.string(),
    responsiblePerson: z.string(),
    targetDate: z.date(),
    status: z.enum(['Pending', 'In Progress', 'Completed']),
    verificationMethod: z.string(),
  })).default([]),
});

type ComprehensiveInvestigationFormValues = z.infer<typeof comprehensiveInvestigationSchema>;

interface ComprehensiveInvestigationFormProps {
  incident: any;
  onSubmit: (data: ComprehensiveInvestigationFormValues) => void;
  isReadOnly?: boolean;
  isNested?: boolean;
}

const ComprehensiveInvestigationForm: React.FC<ComprehensiveInvestigationFormProps> = ({
  incident,
  onSubmit,
  isReadOnly = false,
  isNested = false,
}) => {
  // Initialize form with existing data if available
  const defaultValues = incident?.comprehensiveInvestigation || {
    investigationTeam: {
      leadInvestigator: incident?.leadInvestigator || "",
      teamMembers: [],
      investigationStartDate: undefined,
      investigationEndDate: undefined,
      purposeOfInvestigation: "",
      consequenceOfInterest: "",
      timeframeOfInterest: "",
      peopleOfInterest: "",
      equipmentOfInterest: "",
      activitiesOfInterest: "",
      geographicalBoundariesOfInterest: "",
    },
    sequenceOfEvents: {
      description: "",
      incidentDate: undefined,
      incidentTime: "",
      incidentDescription: "",
      eventDescription: "",
      timeline: [],
      contributingFactors: "",
    },
    informationGatheringPeople: {
      witnessInterviews: [],
      injuredPersonStatement: "",
      supervisorStatement: "",
      otherPersonnelInvolved: "",
    },
    informationGatheringEquipment: {
      equipmentInvolved: "",
      equipmentCondition: "",
      maintenanceRecords: "",
      inspectionRecords: "",
      equipmentDefects: "",
      equipmentPhotos: [],
    },
    informationGatheringMaterial: {
      materialsInvolved: "",
      materialCondition: "",
      materialSpecifications: "",
      materialSafetyDataSheets: "",
      materialHandlingProcedures: "",
      materialDefects: "",
    },
    informationGatheringEnvironment: {
      weatherConditions: "",
      lightingConditions: "",
      noiseLevel: "",
      temperatureConditions: "",
      workspaceLayout: "",
      housekeepingConditions: "",
      environmentalHazards: "",
    },
    informationGatheringMethod: {
      workProcedures: "",
      trainingRecords: "",
      safetyProcedures: "",
      riskAssessments: "",
      permitToWork: "",
      supervisionLevel: "",
      communicationMethods: "",
    },
    investigationConclusions: {
      rootCauses: [],
      contributingFactors: [],
      lessonsLearned: "",
      recommendations: [],
    },
    actionControls: [],
  };

  const form = useForm<ComprehensiveInvestigationFormValues>({
    resolver: zodResolver(comprehensiveInvestigationSchema),
    mode: "onBlur",
    defaultValues,
  });

  const handleSubmit = (data: ComprehensiveInvestigationFormValues) => {
    console.log("ComprehensiveInvestigationForm - handleSubmit called with data:", data);
    onSubmit(data);
  };

  // Styling classes
  const formLabelClass = "text-sm font-medium";
  const sectionTitleClass = "text-lg font-semibold mb-4 flex items-center gap-2";
  const sectionIconClass = "w-5 h-5 text-primary";

  // Add event listeners to all form fields to trigger form submission when nested
  React.useEffect(() => {
    if (isNested) {
      // Subscribe to form changes
      const subscription = form.watch(() => {
        console.log("ComprehensiveInvestigationForm - form changed");
        // Get the current form values and submit them to the parent component
        const currentData = form.getValues();
        console.log("ComprehensiveInvestigationForm - current form values:", currentData);
        onSubmit(currentData);
      });

      // Cleanup subscription on unmount
      return () => subscription.unsubscribe();
    }
  }, [form, onSubmit, isNested]);

  // Create the form content
  const formContent = (
    <div className="space-y-8">
      {/* Investigation Team Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Users className={sectionIconClass} />
            Investigation Team
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <FormField
            control={form.control}
            name="investigationTeam.leadInvestigator"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Lead Investigator (Required)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Name of the lead investigator"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Team Members Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Team Members</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentMembers = form.getValues("investigationTeam.teamMembers") || [];
                    form.setValue("investigationTeam.teamMembers", [
                      ...currentMembers,
                      { name: "", designation: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Team Member
                </Button>
              )}
            </div>

            {form.watch("investigationTeam.teamMembers")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Team Member {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentMembers = form.getValues("investigationTeam.teamMembers") || [];
                        const updatedMembers = currentMembers.filter((_, i) => i !== index);
                        form.setValue("investigationTeam.teamMembers", updatedMembers);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`investigationTeam.teamMembers.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Team member name"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`investigationTeam.teamMembers.${index}.designation`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Designation</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Job title/designation"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("investigationTeam.teamMembers") || form.watch("investigationTeam.teamMembers")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No team members added. Click "Add Team Member" to add one.
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="investigationTeam.investigationStartDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel className={formLabelClass}>Investigation Start Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isReadOnly}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.investigationEndDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel className={formLabelClass}>Investigation End Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isReadOnly}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Purpose and Scope Section */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Investigation Scope & Purpose</h3>

            <FormField
              control={form.control}
              name="investigationTeam.purposeOfInvestigation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Purpose of Investigation</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the purpose and objectives of this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.consequenceOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Consequence of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the consequences or outcomes of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.timeframeOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Timeframe of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Specify the timeframe or period of interest for this investigation"
                      className="min-h-[60px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.peopleOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>People of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Identify key people of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.equipmentOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Equipment of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Identify equipment, tools, or machinery of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.activitiesOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Activities of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe activities, processes, or operations of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="investigationTeam.geographicalBoundariesOfInterest"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Geographical Boundaries of Interest</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Define the geographical boundaries or locations of interest for this investigation"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Sequence of Events Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Clock className={sectionIconClass} />
            Sequence of Events Leading to Damage / Incident
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Incident Information */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Basic Incident Information</h3>

            <FormField
              control={form.control}
              name="sequenceOfEvents.description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide a brief description of the incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="sequenceOfEvents.incidentDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className={formLabelClass}>Date (dd-mm-yyyy)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={isReadOnly}
                          >
                            {field.value ? (
                              format(field.value, "dd-MM-yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sequenceOfEvents.incidentTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={formLabelClass}>Time</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 14:30 or 2:30 PM"
                        disabled={isReadOnly}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="sequenceOfEvents.incidentDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Incident Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide a detailed description of what happened during the incident"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Detailed Analysis */}
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 border-b pb-2">Detailed Analysis</h3>

            <FormField
              control={form.control}
              name="sequenceOfEvents.eventDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Event Description (Required)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide a detailed description of the sequence of events leading to the incident"
                      className="min-h-[120px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sequenceOfEvents.contributingFactors"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Contributing Factors</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe any contributing factors that led to the incident"
                      className="min-h-[100px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Timeline Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Timeline of Events</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentTimeline = form.getValues("sequenceOfEvents.timeline") || [];
                    form.setValue("sequenceOfEvents.timeline", [
                      ...currentTimeline,
                      { time: "", event: "", evidence: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Timeline Entry
                </Button>
              )}
            </div>

            {form.watch("sequenceOfEvents.timeline")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Timeline Entry {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentTimeline = form.getValues("sequenceOfEvents.timeline") || [];
                        const updatedTimeline = currentTimeline.filter((_, i) => i !== index);
                        form.setValue("sequenceOfEvents.timeline", updatedTimeline);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.time`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Time</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., 09:30 AM"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.event`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Event</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Describe what happened"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`sequenceOfEvents.timeline.${index}.evidence`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Evidence</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Supporting evidence"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("sequenceOfEvents.timeline") || form.watch("sequenceOfEvents.timeline")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No timeline entries added. Click "Add Timeline Entry" to add one.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Information Gathering - People Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Users className={sectionIconClass} />
            Information Gathering (A. People)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="informationGatheringPeople.injuredPersonStatement"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Injured Person Statement</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Statement from the injured person"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringPeople.supervisorStatement"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Supervisor Statement</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Statement from the supervisor"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringPeople.otherPersonnelInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Other Personnel Involved</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about other personnel involved"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Witness Interviews Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Witness Interviews</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentInterviews = form.getValues("informationGatheringPeople.witnessInterviews") || [];
                    form.setValue("informationGatheringPeople.witnessInterviews", [
                      ...currentInterviews,
                      { name: "", role: "", statement: "", dateInterviewed: new Date() }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Witness Interview
                </Button>
              )}
            </div>

            {form.watch("informationGatheringPeople.witnessInterviews")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Witness Interview {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentInterviews = form.getValues("informationGatheringPeople.witnessInterviews") || [];
                        const updatedInterviews = currentInterviews.filter((_, i) => i !== index);
                        form.setValue("informationGatheringPeople.witnessInterviews", updatedInterviews);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`informationGatheringPeople.witnessInterviews.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Witness Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name of the witness"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`informationGatheringPeople.witnessInterviews.${index}.role`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Role/Position</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Role or position"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`informationGatheringPeople.witnessInterviews.${index}.statement`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Statement</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Witness statement"
                          className="min-h-[80px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`informationGatheringPeople.witnessInterviews.${index}.dateInterviewed`}
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="text-xs">Date Interviewed</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                              disabled={isReadOnly}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </FormItem>
                  )}
                />
              </div>
            ))}

            {(!form.watch("informationGatheringPeople.witnessInterviews") || form.watch("informationGatheringPeople.witnessInterviews")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No witness interviews added. Click "Add Witness Interview" to add one.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Information Gathering - Equipment Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Wrench className={sectionIconClass} />
            Information Gathering (B. Equipment)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="informationGatheringEquipment.equipmentInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Equipment Involved</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the equipment involved in the incident"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEquipment.equipmentCondition"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Equipment Condition</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the condition of the equipment"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEquipment.maintenanceRecords"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Maintenance Records</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about maintenance records"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEquipment.inspectionRecords"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Inspection Records</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about inspection records"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEquipment.equipmentDefects"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Equipment Defects</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any equipment defects found"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Information Gathering - Material Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Package className={sectionIconClass} />
            Information Gathering (C. Material)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialsInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Materials Involved</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the materials involved in the incident"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialCondition"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Material Condition</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the condition of the materials"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialSpecifications"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Material Specifications</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Material specifications and requirements"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialSafetyDataSheets"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Material Safety Data Sheets</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information from material safety data sheets"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialHandlingProcedures"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Material Handling Procedures</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Material handling procedures and protocols"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMaterial.materialDefects"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Material Defects</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any material defects found"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Information Gathering - Environment Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Cloud className={sectionIconClass} />
            Information Gathering (D. Environment)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="informationGatheringEnvironment.weatherConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Weather Conditions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Weather conditions at the time of incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEnvironment.lightingConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Lighting Conditions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Lighting conditions at the time of incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEnvironment.noiseLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Noise Level</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Noise level at the time of incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="informationGatheringEnvironment.temperatureConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={formLabelClass}>Temperature Conditions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Temperature conditions at the time of incident"
                      className="min-h-[80px]"
                      disabled={isReadOnly}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.workspaceLayout"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Workspace Layout</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the workspace layout and organization"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.housekeepingConditions"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Housekeeping Conditions</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the housekeeping conditions"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringEnvironment.environmentalHazards"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Environmental Hazards</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any environmental hazards present"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Information Gathering - Method Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Settings className={sectionIconClass} />
            Information Gathering (E. Method)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="informationGatheringMethod.workProcedures"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Work Procedures</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the work procedures in place"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.trainingRecords"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Training Records</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about training records"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.safetyProcedures"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Safety Procedures</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the safety procedures"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.riskAssessments"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Risk Assessments</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about risk assessments"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.permitToWork"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Permit to Work</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Information about permit to work systems"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.supervisionLevel"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Supervision Level</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the level of supervision"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="informationGatheringMethod.communicationMethods"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Communication Methods</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the communication methods used"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Conclusions Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <FileText className={sectionIconClass} />
            Conclusions of Investigation Team
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="investigationConclusions.lessonsLearned"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Lessons Learned</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the lessons learned from this investigation"
                    className="min-h-[120px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Root Causes Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Root Causes</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentCauses = form.getValues("investigationConclusions.rootCauses") || [];
                    form.setValue("investigationConclusions.rootCauses", [
                      ...currentCauses,
                      { category: "", description: "", evidence: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Root Cause
                </Button>
              )}
            </div>

            {form.watch("investigationConclusions.rootCauses")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Root Cause {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentCauses = form.getValues("investigationConclusions.rootCauses") || [];
                        const updatedCauses = currentCauses.filter((_, i) => i !== index);
                        form.setValue("investigationConclusions.rootCauses", updatedCauses);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name={`investigationConclusions.rootCauses.${index}.category`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Category</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Human Factor, Equipment, Environment"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`investigationConclusions.rootCauses.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the root cause"
                          className="min-h-[80px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`investigationConclusions.rootCauses.${index}.evidence`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Evidence</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Supporting evidence for this root cause"
                          className="min-h-[80px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            ))}

            {(!form.watch("investigationConclusions.rootCauses") || form.watch("investigationConclusions.rootCauses")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No root causes added. Click "Add Root Cause" to add one.
              </div>
            )}
          </div>

          {/* Contributing Factors Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Contributing Factors</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentFactors = form.getValues("investigationConclusions.contributingFactors") || [];
                    form.setValue("investigationConclusions.contributingFactors", [
                      ...currentFactors,
                      { factor: "", impact: "" }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Contributing Factor
                </Button>
              )}
            </div>

            {form.watch("investigationConclusions.contributingFactors")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Contributing Factor {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentFactors = form.getValues("investigationConclusions.contributingFactors") || [];
                        const updatedFactors = currentFactors.filter((_, i) => i !== index);
                        form.setValue("investigationConclusions.contributingFactors", updatedFactors);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`investigationConclusions.contributingFactors.${index}.factor`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Factor</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Contributing factor"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`investigationConclusions.contributingFactors.${index}.impact`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Impact</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Impact of this factor"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}

            {(!form.watch("investigationConclusions.contributingFactors") || form.watch("investigationConclusions.contributingFactors")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No contributing factors added. Click "Add Contributing Factor" to add one.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assign Action Controls Section */}
      <Card>
        <CardHeader>
          <CardTitle className={sectionTitleClass}>
            <Target className={sectionIconClass} />
            Assign Action Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className={formLabelClass}>Action Controls</Label>
              {!isReadOnly && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentActions = form.getValues("actionControls") || [];
                    form.setValue("actionControls", [
                      ...currentActions,
                      {
                        actionType: "Immediate",
                        description: "",
                        responsiblePerson: "",
                        targetDate: new Date(),
                        status: "Pending",
                        verificationMethod: ""
                      }
                    ]);
                  }}
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Action Control
                </Button>
              )}
            </div>

            {form.watch("actionControls")?.map((_, index) => (
              <div key={index} className="border rounded-md p-4 bg-slate-50 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Action Control {index + 1}</h4>
                  {!isReadOnly && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentActions = form.getValues("actionControls") || [];
                        const updatedActions = currentActions.filter((_, i) => i !== index);
                        form.setValue("actionControls", updatedActions);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.actionType`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Action Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isReadOnly}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select action type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Immediate">Immediate</SelectItem>
                            <SelectItem value="Short-term">Short-term</SelectItem>
                            <SelectItem value="Long-term">Long-term</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.status`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isReadOnly}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Pending">Pending</SelectItem>
                            <SelectItem value="In Progress">In Progress</SelectItem>
                            <SelectItem value="Completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`actionControls.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the action control measure"
                          className="min-h-[80px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.responsiblePerson`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Responsible Person</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name of responsible person"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`actionControls.${index}.targetDate`}
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-xs">Target Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                                disabled={isReadOnly}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`actionControls.${index}.verificationMethod`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Verification Method</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="How will completion of this action be verified?"
                          className="min-h-[60px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            ))}

            {(!form.watch("actionControls") || form.watch("actionControls")?.length === 0) && (
              <div className="text-center py-4 text-slate-500 border-2 border-dashed border-slate-200 rounded-md">
                No action controls added. Click "Add Action Control" to add one.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      {!isReadOnly && !isNested && (
        <div className="flex justify-end pt-6">
          <Button type="submit" className="bg-green-600 hover:bg-green-700 text-white px-8">
            <CheckCircle className="w-4 h-4 mr-2" />
            Submit Comprehensive Investigation
          </Button>
        </div>
      )}
    </div>
  );

  // Return different structures based on whether this is nested in another form
  return isNested ? (
    formContent
  ) : (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {formContent}
      </form>
    </Form>
  );
};

export default ComprehensiveInvestigationForm;
